* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: rgb(11, 8, 16);
    color: rgba(255, 255, 255, 0.9);
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, #8a63f2 0%, #6a4bc7 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.3);
}

.sidebar-header {
    padding: 0 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
}

.nav-item {
    margin: 5px 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-item a:hover,
.nav-item.active a {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-right: 3px solid #fff;
}

.nav-item i {
    margin-right: 10px;
    width: 20px;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background-color: rgb(11, 8, 16);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.section-header h1 {
    color: #fff;
    font-size: 2rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(138, 99, 242, 0.4);
}

.search-bar {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    border-radius: 25px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #8a63f2;
}

.search-bar i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

/* Users Grid */
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.user-card {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(138, 99, 242, 0.2);
}

.user-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.user-info h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.user-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.user-actions {
    position: relative;
}

.three-dots {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.three-dots:hover {
    background-color: #f8f9fa;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgb(30, 25, 40);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 8px 0;
    min-width: 120px;
    z-index: 1000;
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
}

.user-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.stat {
    text-align: center;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #8a63f2;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.pagination button {
    background: #8a63f2;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination button:hover:not(:disabled) {
    background: #6a4bc7;
}

.pagination button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

#pageInfo {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

/* Buttons */
.btn-primary {
    background: #8a63f2;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(138, 99, 242, 0.3);
}

.btn-primary:hover {
    background: #6a4bc7;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(138, 99, 242, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.btn-danger:hover {
    background: #c82333;
}

/* Content Controls */
.content-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    flex: 1;
}

.content-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.info-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-style: italic;
}

.level-controls {
    display: flex;
    gap: 10px;
}

/* Levels Container */
.levels-container {
    display: grid;
    gap: 25px;
}

/* Empty Levels State */
.empty-levels-state {
    text-align: center;
    padding: 60px 20px;
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.empty-levels-state .empty-icon {
    font-size: 4rem;
    color: rgba(138, 99, 242, 0.6);
    margin-bottom: 20px;
}

.empty-levels-state h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.empty-levels-state p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin-bottom: 30px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Level Card Styles */
.level-card {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.level-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(138, 99, 242, 0.15);
}

.level-header {
    background: linear-gradient(135deg, rgba(138, 99, 242, 0.2), rgba(106, 75, 199, 0.2));
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.level-header:hover {
    background: linear-gradient(135deg, rgba(138, 99, 242, 0.25), rgba(106, 75, 199, 0.25));
}

.level-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(135deg, #8a63f2, #6a4bc7);
    border-radius: 0 2px 2px 0;
    opacity: 0.7;
}

.level-card {
    position: relative;
}

.level-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.level-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.level-info h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.3rem;
    margin: 0;
    font-weight: 600;
    flex: 1;
}

.level-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.dropdown-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 1);
    transform: scale(1.05);
}

.dropdown-toggle i {
    transition: transform 0.3s ease;
}

.level-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.level-status.active {
    background: #d4edda;
    color: #155724;
}

.level-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.questions-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.level-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-toggle {
    padding: 8px 15px;
    font-size: 0.85rem;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.level-questions {
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    transform-origin: top;
}

.level-questions.collapsed {
    max-height: 0;
    padding: 0 20px;
    opacity: 0;
    transform: scaleY(0);
}

.level-questions.expanded {
    max-height: 2000px; /* Large enough to accommodate content */
    padding: 20px;
    opacity: 1;
    transform: scaleY(1);
}

.level-questions.empty {
    text-align: center;
}

.level-questions.empty.collapsed {
    padding: 0 20px;
}

.level-questions.empty.expanded {
    padding: 40px 20px;
}

.empty-questions {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    font-size: 1rem;
}

.question-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.question-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(138, 99, 242, 0.3);
}

.question-item:last-child {
    margin-bottom: 0;
}

.question-item .question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.question-item .question-text {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
    margin-right: 15px;
    line-height: 1.4;
}

.question-item .question-actions {
    display: flex;
    gap: 8px;
}

.question-item .question-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
}

.difficulty-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.difficulty-easy {
    background: #d4edda;
    color: #155724;
}

.difficulty-medium {
    background: #fff3cd;
    color: #856404;
}

.difficulty-hard {
    background: #f8d7da;
    color: #721c24;
}

.question-item .options-list {
    list-style: none;
    margin: 12px 0 0 0;
}

.question-item .options-list li {
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.question-item .options-list li:last-child {
    border-bottom: none;
}

.option-label {
    font-weight: 600;
    margin-right: 10px;
    color: #8a63f2;
    min-width: 25px;
}

.correct-answer {
    background: #d4edda;
    color: #155724;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: rgb(30, 25, 40);
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.5rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 25px;
}

/* User Details Modal */
.user-detail-section {
    margin-bottom: 25px;
}

.user-detail-section h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid #8a63f2;
    padding-bottom: 5px;
}

.user-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 8px;
}

.info-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.info-value {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
}

.progress-section {
    margin: 20px 0;
}

.progress-item {
    margin-bottom: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
}

.progress-value {
    font-weight: 600;
    color: #8a63f2;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #8a63f2, #ff9e3f);
    border-radius: 6px;
    transition: width 0.3s ease;
}

.tier-display {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #8a63f2, #6a4bc7);
    color: white;
    border-radius: 12px;
    margin: 20px 0;
}

.tier-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.tier-exp {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #8a63f2;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 15px 0;
    }

    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
    }

    .nav-item {
        margin: 0 5px;
        white-space: nowrap;
    }

    .main-content {
        padding: 20px 15px;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .users-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .user-basic-info {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .level-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .level-title-row {
        flex-direction: row;
        justify-content: space-between;
    }

    .level-meta {
        justify-content: flex-start;
    }

    .level-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .question-item .question-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .question-item .question-actions {
        justify-content: center;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    min-width: 300px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification-error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.notification-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.notification button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.notification button:hover {
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* Pending Changes and Editing States */
.question-item.pending-changes {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.1);
}

.question-item.editing {
    border-left: 4px solid #17a2b8;
    background: rgba(23, 162, 184, 0.1);
}

.pending-indicator {
    color: #ffc107;
    font-size: 0.8rem;
    font-weight: 500;
}

.pending-changes-indicator {
    color: #ffc107;
    font-size: 0.8rem;
    font-weight: 500;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.btn-success {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-success:hover {
    background: #218838;
}

.btn-small {
    padding: 6px 12px;
    font-size: 11px;
}

.level-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}
