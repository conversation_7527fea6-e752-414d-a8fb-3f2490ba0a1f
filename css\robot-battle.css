/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Courier New', monospace;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0A0A1A;
    color: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow: hidden;
}

.game-container {
    width: 100%;
    max-width: 1100px;
    height: 90vh;
    max-height: 700px;
    background-color: #121212;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 240, 255, 0.3);
    display: flex;
    flex-direction: column;
    padding: 15px;
    margin: 10px auto;
    overflow: hidden;
}

/* Battle Arena */
.battle-arena {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50%;
    padding: 0 10px;
    margin-bottom: 5px;
}

.player-side, .enemy-side {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 40%;
    height: 100%;
}

.vs-indicator {
    font-size: 3rem;
    color: #00F0FF;
    text-shadow: 0 0 10px #00F0FF;
    animation: pulse 2s infinite;
}

/* Robot Styling */
.robot {
    width: 120px;
    height: 160px;
    position: relative;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

/* Damage Effects */
.damage-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 120px 160px;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* Player damage states */
.player-robot.damaged .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M30,40 L40,30 M90,40 L80,30 M35,60 L45,50 M85,60 L75,50 M25,80 L35,70 M95,80 L85,70 M20,100 L30,90 M100,100 L90,90" stroke="rgba(255,152,0,0.8)" stroke-width="2.5" stroke-linecap="round"/><circle cx="60" cy="110" r="4" fill="rgba(255,152,0,0.6)"/><circle cx="40" cy="70" r="2" fill="rgba(255,152,0,0.4)"/><circle cx="80" cy="70" r="2" fill="rgba(255,152,0,0.4)"/></svg>');
    opacity: 0.8;
}

.player-robot.critical .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M25,35 L35,25 M95,35 L85,25 M30,70 L45,55 M90,70 L75,55 M35,90 L50,75 M85,90 L70,75 M20,110 L30,100 M100,110 L90,100 M15,130 L25,120 M105,130 L95,120" stroke="rgba(244,67,54,1)" stroke-width="3.5" stroke-linecap="round"/><circle cx="60" cy="140" r="5" fill="rgba(244,67,54,0.7)"/><circle cx="40" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="80" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="30" cy="60" r="2" fill="rgba(244,67,54,0.3)"/><circle cx="90" cy="60" r="2" fill="rgba(244,67,54,0.3)"/></svg>');
    opacity: 1;
}

/* Enemy damage states */
.enemy-robot.damaged .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M30,40 L40,30 M90,40 L80,30 M35,60 L45,50 M85,60 L75,50 M25,80 L35,70 M95,80 L85,70 M20,100 L30,90 M100,100 L90,90" stroke="rgba(255,152,0,0.8)" stroke-width="2.5" stroke-linecap="round"/><circle cx="60" cy="110" r="4" fill="rgba(255,152,0,0.6)"/><circle cx="40" cy="70" r="2" fill="rgba(255,152,0,0.4)"/><circle cx="80" cy="70" r="2" fill="rgba(255,152,0,0.4)"/></svg>');
    opacity: 0.8;
}

.enemy-robot.critical .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M25,35 L35,25 M95,35 L85,25 M30,70 L45,55 M90,70 L75,55 M35,90 L50,75 M85,90 L70,75 M20,110 L30,100 M100,110 L90,100 M15,130 L25,120 M105,130 L95,120" stroke="rgba(244,67,54,1)" stroke-width="3.5" stroke-linecap="round"/><circle cx="60" cy="140" r="5" fill="rgba(244,67,54,0.7)"/><circle cx="40" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="80" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="30" cy="60" r="2" fill="rgba(244,67,54,0.3)"/><circle cx="90" cy="60" r="2" fill="rgba(244,67,54,0.3)"/></svg>');
    opacity: 1;
}

/* Enhanced Robot Facial Expressions with sad and cracked effects */
.robot.victory .robot-mouth {
    border-radius: 15px 15px 0 0;
    transform: translateY(-5px) scaleY(1.2);
    background-color: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.robot.victory .robot-eye {
    animation: victory-blink 2s infinite;
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.robot.defeat .robot-mouth {
    border-radius: 0 0 15px 15px;
    transform: translateY(10px) scaleY(0.6);
    background-color: #f44336;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
}

.robot.defeat .robot-eye {
    opacity: 0.3;
    animation: defeat-blink 3s infinite;
    background-color: #f44336;
}

/* Sad and cracked expressions for damage states */
/* Light damage state (75-50% health) */
.robot.lightly-damaged .robot-mouth {
    border-radius: 0 0 12px 12px;
    transform: translateY(4px) scaleY(0.85);
    background-color: #ffc107;
    box-shadow: 0 0 6px rgba(255, 193, 7, 0.3);
    position: relative;
}

.robot.lightly-damaged .robot-eye {
    opacity: 0.8;
    animation: light-sad-blink 3s infinite;
    background-color: #ffc107;
    transform: scaleY(0.95);
}

/* Medium damage state (50-25% health) */
.robot.damaged .robot-mouth {
    border-radius: 0 0 15px 15px;
    transform: translateY(8px) scaleY(0.7);
    background-color: #ff9800;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
    position: relative;
}

.robot.damaged .robot-mouth::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 3px;
    height: 10px;
    background: rgba(255, 255, 255, 0.7);
    transform: translate(-50%, -50%);
    border-radius: 1px;
    animation: sad-tear 1.8s infinite;
}

.robot.critical .robot-mouth {
    border-radius: 0 0 15px 15px;
    transform: translateY(12px) scaleY(0.5);
    background-color: #f44336;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.6);
    position: relative;
}

.robot.critical .robot-mouth::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 15px;
    background: rgba(255, 255, 255, 0.9);
    transform: translate(-50%, -50%);
    border-radius: 1px;
    animation: sad-tear 1.2s infinite;
}

/* Sad eye expressions with tears */
.robot.damaged .robot-eye {
    opacity: 0.6;
    animation: sad-blink 2s infinite;
    background-color: #ff9800;
    position: relative;
    transform: scaleY(0.9);
}

.robot.damaged .robot-eye::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 3px;
    height: 8px;
    background: rgba(255, 255, 255, 0.6);
    transform: translateX(-50%);
    border-radius: 1px;
    animation: eye-tear 2.5s infinite;
}

.robot.critical .robot-eye {
    opacity: 0.3;
    animation: critical-sad-blink 1.5s infinite;
    background-color: #f44336;
    position: relative;
    transform: scaleY(0.8);
}

.robot.critical .robot-eye::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    width: 4px;
    height: 10px;
    background: rgba(255, 255, 255, 0.8);
    transform: translateX(-50%);
    border-radius: 1px;
    animation: eye-tear 1.8s infinite;
}

/* Crack effects on robot head */
/* Light damage head effects */
.robot.lightly-damaged .robot-head {
    box-shadow: 0 0 12px rgba(255, 193, 7, 0.3), 0 0 12px rgba(0, 240, 255, 0.5);
    position: relative;
    filter: brightness(0.95);
}

.robot.lightly-damaged .robot-head::before {
    content: '';
    position: absolute;
    top: 25%;
    left: 35%;
    width: 30%;
    height: 50%;
    background: linear-gradient(45deg, transparent 45%, rgba(255, 193, 7, 0.3) 50%, transparent 55%);
    border-radius: 50%;
    animation: light-crack-pulse 3s infinite;
}

/* Medium damage head effects */
.robot.damaged .robot-head {
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.4), 0 0 15px rgba(0, 240, 255, 0.4);
    position: relative;
    filter: brightness(0.9);
}

.robot.damaged .robot-head::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 30%;
    width: 40%;
    height: 60%;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 152, 0, 0.4) 50%, transparent 60%);
    border-radius: 50%;
    animation: crack-pulse 2.5s infinite;
}

.robot.critical .robot-head {
    box-shadow: 0 0 20px rgba(244, 67, 54, 0.5), 0 0 15px rgba(0, 240, 255, 0.3);
    filter: brightness(0.7);
    position: relative;
}

.robot.critical .robot-head::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 25%;
    width: 50%;
    height: 70%;
    background: 
        linear-gradient(45deg, transparent 35%, rgba(244, 67, 54, 0.5) 45%, transparent 55%),
        linear-gradient(-45deg, transparent 40%, rgba(244, 67, 54, 0.4) 50%, transparent 60%);
    border-radius: 50%;
    animation: critical-crack-pulse 1.8s infinite;
}

/* Enhanced robot head glow effects based on state */
.robot.victory .robot-head {
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.6), 0 0 15px rgba(0, 240, 255, 0.5);
}

.robot.defeat .robot-head {
    box-shadow: 0 0 15px rgba(244, 67, 54, 0.4), 0 0 15px rgba(0, 240, 255, 0.3);
    filter: brightness(0.8);
}

/* Shake animation when taking damage */
@keyframes robot-hurt {
    0%, 100% { 
        transform: translateX(0) rotate(0deg); 
    }
    25% { 
        transform: translateX(-8px) rotate(-2deg); 
    }
    75% { 
        transform: translateX(8px) rotate(2deg); 
    }
}

.robot.hurt {
    animation: robot-hurt 0.4s ease-in-out;
}

/* Smoke effect for defeated robot */
.defeated .robot-body::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 20px;
    background: rgba(100, 100, 100, 0.5);
    border-radius: 50%;
    filter: blur(5px);
    animation: smoke 2s ease-out infinite;
}

@keyframes smoke {
    0% { transform: translateX(-50%) scale(1); opacity: 0.5; }
    100% { transform: translateX(-50%) scale(2); opacity: 0; }
}

.robot-sprite {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Player Robot - Stationary (no bounce) */
.player-robot .robot-sprite {
    position: relative;
}

/* Simplified main body - one large circle */
.player-robot .robot-sprite .robot-head {
    position: absolute;
    top: 30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #00F0FF, #0088FF);
    border-radius: 50%;
    border: 3px solid #FFFFFF;
    box-shadow: 0 0 15px rgba(0, 240, 255, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.player-robot .robot-sprite .robot-eyes {
    display: flex;
    gap: 25px;
    margin-bottom: 8px;
}

.player-robot .robot-sprite .robot-eye {
    width: 18px;
    height: 18px;
    background: #000;
    border-radius: 50%;
    animation: blink 4s infinite;
}

.player-robot .robot-sprite .robot-mouth {
    width: 30px;
    height: 15px;
    border: 3px solid #000;
    border-top: none;
    border-radius: 0 0 30px 30px;
    background: transparent;
}

.player-robot .robot-sprite .robot-cheeks {
    position: absolute;
    top: 55px;
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.player-robot .robot-sprite .robot-cheek {
    width: 15px;
    height: 15px;
    background: #FFB6C1;
    border-radius: 50%;
    animation: cheek-glow 4s ease-in-out infinite;
}

.player-robot .robot-sprite .robot-antenna {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 25px;
    background: #FFFFFF;
    border-radius: 2px;
}

.player-robot .robot-sprite .robot-antenna::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -6px;
    width: 16px;
    height: 16px;
    background: #00FF00;
    border-radius: 50%;
    animation: antenna-blink 2s infinite;
}

/* Simple circular body */
.player-robot .robot-sprite .robot-body {
    position: absolute;
    top: 160px;
    width: 80px;
    height: 40px;
    background: #2A2A3A;
    border-radius: 40px;
    border: 2px solid #00F0FF;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 20px;
}

.player-robot .robot-sprite .robot-panel {
    width: 12px;
    height: 12px;
    background: #00F0FF;
    border-radius: 50%;
    animation: panel-glow 3s ease-in-out infinite;
}

/* Hide complex parts for simplicity */
.player-robot .robot-sprite .robot-arms {
    display: none;
}

.player-robot .robot-sprite .robot-legs {
    display: none;
}

/* Enemy Robot - Stationary (no bounce) */
.enemy-robot .robot-sprite {
    position: relative;
}

/* Simplified main body - one large circle */
.enemy-robot .robot-sprite .robot-head {
    position: absolute;
    top: 30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #FF0055, #FF5500);
    border-radius: 50%;
    border: 3px solid #FFFFFF;
    box-shadow: 0 0 15px rgba(255, 0, 85, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.enemy-robot .robot-sprite .robot-eyes {
    display: flex;
    gap: 25px;
    margin-bottom: 8px;
}

.enemy-robot .robot-sprite .robot-eye {
    width: 18px;
    height: 18px;
    background: #000;
    border-radius: 50%;
    animation: blink 3.5s infinite;
}

.enemy-robot .robot-sprite .robot-mouth {
    width: 30px;
    height: 15px;
    border: 3px solid #000;
    border-top: none;
    border-radius: 0 0 30px 30px;
    background: transparent;
}

.enemy-robot .robot-sprite .robot-cheeks {
    position: absolute;
    top: 55px;
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.enemy-robot .robot-sprite .robot-cheek {
    width: 15px;
    height: 15px;
    background: #FFB6C1;
    border-radius: 50%;
    animation: cheek-glow 3.5s ease-in-out infinite;
}

.enemy-robot .robot-sprite .robot-antenna {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 25px;
    background: #FFFFFF;
    border-radius: 2px;
}

.enemy-robot .robot-sprite .robot-antenna::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -6px;
    width: 16px;
    height: 16px;
    background: #FF0000;
    border-radius: 50%;
    animation: antenna-blink 1.5s infinite;
}

/* Simple circular body */
.enemy-robot .robot-sprite .robot-body {
    position: absolute;
    top: 160px;
    width: 80px;
    height: 40px;
    background: #3A2A2A;
    border-radius: 40px;
    border: 2px solid #FF0055;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 20px;
}

.enemy-robot .robot-sprite .robot-panel {
    width: 12px;
    height: 12px;
    background: #FF0055;
    border-radius: 50%;
    animation: panel-glow 2.5s ease-in-out infinite;
}

/* Hide complex parts for simplicity */
.enemy-robot .robot-sprite .robot-arms {
    display: none;
}

.enemy-robot .robot-sprite .robot-legs {
    display: none;
}

/* Health Bars */
.health-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.health-label {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #00F0FF;
}

.health-bar-container {
    background: #222;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 8px #00F0FF;
    border: 1px solid #00F0FF;
}

.health-bar {
    background: linear-gradient(90deg, #00F0FF, #0088FF);
    height: 16px;
    border-radius: 10px;
    transition: width 0.3s;
}

.player-health {
    width: 100%;
    background: linear-gradient(90deg, #00F0FF, #0088FF);
}

.enemy-health {
    width: 100%;
    background: linear-gradient(90deg, #FF0055, #FF5500);
}

.health-value {
    font-size: 1rem;
    margin-top: 5px;
    color: #00F0FF;
    font-weight: bold;
    text-shadow: 0 0 5px #00F0FF;
}

/* Power Ups */
.power-ups {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 5px 0;
    padding: 5px;
    flex-wrap: wrap;
}

.power-up-btn {
    background: linear-gradient(135deg, #2A2A3A, #1A1A2A);
    border: 2px solid #00F0FF;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #FFFFFF;
    font-size: 1.2rem;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.3);
    flex-shrink: 0;
}

.power-up-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.power-up-btn:active {
    transform: translateY(0);
}

.power-up-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.power-up-count {
    position: absolute;
    bottom: -10px;
    right: -5px;
    background: #FF4081;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Active power-up effect */
.power-up-active {
    animation: pulse-glow 1.5s infinite;
    border-color: #FFD700;
    box-shadow: 0 0 15px #FFD700;
}

@keyframes pulse-glow {
    0% { box-shadow: 0 0 5px #FFD700; }
    50% { box-shadow: 0 0 20px #FFD700; }
    100% { box-shadow: 0 0 5px #FFD700; }
}

/* Question Area */
.question-area {
    background-color: rgba(10, 10, 26, 0.7);
    border-radius: 8px;
    border: 1px solid #00F0FF;
    box-shadow: 0 0 15px rgba(0, 240, 255, 0.2);
    padding: 10px;
}

.timer-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.timer {
    width: 80%;
    height: 30px;
    background-color: #333;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.timer-bar {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #00F0FF, #0088FF);
    border-radius: 15px;
    transition: width 0.1s linear;
}

.timer-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #FFFFFF;
    font-weight: bold;
    font-size: 1.2rem;
}

.question-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.question-text {
    font-size: 1.1rem;
    margin-bottom: 15px;
    text-align: center;
    color: #FFFFFF;
    text-shadow: 0 0 10px rgba(0, 240, 255, 0.7);
    line-height: 1.3;
    padding: 0 5px;
}

.options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 5px;
    padding: 0 5px;
}

.option-btn {
    background-color: #1A1A2A;
    color: #FFFFFF;
    border: 2px solid #00F0FF;
    border-radius: 5px;
    padding: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 0 5px rgba(0, 240, 255, 0.3);
}

.option-btn:hover {
    background-color: #2A2A3A;
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.option-btn:active {
    transform: scale(0.98);
}

/* Unified Modal Styles for Game1 (copied/adapted from style_millionaire.css) */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}
.modal.show {
    display: flex;
}
.modal-content {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.3);
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: modalAppear 0.5s ease-out;
}
@keyframes modalAppear {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}
.result-title, .modal h2 {
    color: #4fc3f7;
    margin-bottom: 25px;
    font-size: 2.5rem;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
    font-weight: normal;
}
.stars {
    font-size: 2.5rem;
    margin: 20px 0;
    letter-spacing: 10px;
}
.star { color: #ffd700; }
.star.empty { color: #333; }
.result-message, #result-message {
    color: #fff;
    font-size: 1.2rem;
    margin: 20px 0;
    line-height: 1.5;
}
.exp-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: expPulse 2s infinite;
}
@keyframes expPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 15px rgba(79, 195, 247, 0.2); }
    50% { transform: scale(1.05); box-shadow: 0 0 25px rgba(79, 195, 247, 0.4); }
}
.exp-icon { font-size: 1.8rem; animation: sparkle 1.5s infinite; }
@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.2) rotate(15deg); opacity: 0.8; }
}
.exp-text { color: #ffeb3b; font-size: 1.5rem; font-weight: bold; text-shadow: 0 0 10px rgba(255, 235, 59, 0.5); }
.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}
.replay-btn, .main-menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.replay-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    box-shadow: 0 4px 0 #2fa3d7;
}
.replay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}
.main-menu-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    box-shadow: 0 4px 0 #b71c1c;
}
.main-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #b71c1c;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes idle-bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

@keyframes blink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes antenna-blink {
    0%, 50% { opacity: 1; box-shadow: 0 0 5px currentColor; }
    25%, 75% { opacity: 0.3; box-shadow: none; }
}

@keyframes panel-glow {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; box-shadow: 0 0 10px currentColor; }
}

@keyframes cheek-glow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.9; transform: scale(1.1); }
}

@keyframes arm-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(5deg); }
}

.damage-effect {
    position: absolute;
    top: -20px;
    left: -20px;
    width: calc(100% + 40px);
    height: calc(100% + 40px);
    background: radial-gradient(circle, rgba(255, 50, 50, 0.9) 0%, rgba(255, 100, 100, 0.6) 30%, rgba(255, 0, 0, 0) 70%);
    opacity: 0;
    pointer-events: none;
    border-radius: 50%;
    animation: damage-pulse 0.6s ease-out;
}

.attack-animation {
    animation: attack 0.8s ease-in-out;
}

@keyframes attack {
    0% {
        transform: scale(1);
        filter: brightness(1) drop-shadow(0 0 5px rgba(0, 240, 255, 0.3));
    }
    15% {
        transform: scale(1.05);
        filter: brightness(1.3) drop-shadow(0 0 15px rgba(0, 240, 255, 0.8));
    }
    40% {
        transform: scale(1.1);
        filter: brightness(1.5) drop-shadow(0 0 25px rgba(0, 240, 255, 1));
    }
    60% {
        transform: scale(1.08);
        filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 240, 255, 0.7));
    }
    80% {
        transform: scale(1.03);
        filter: brightness(1.1) drop-shadow(0 0 10px rgba(0, 240, 255, 0.5));
    }
    100% {
        transform: scale(1);
        filter: brightness(1) drop-shadow(0 0 5px rgba(0, 240, 255, 0.3));
    }
}

.enemy-attack-animation {
    animation: enemy-attack 0.8s ease-in-out;
}

@keyframes enemy-attack {
    0% {
        transform: scale(1) scaleX(-1);
        filter: brightness(1) drop-shadow(0 0 5px rgba(255, 0, 85, 0.3));
    }
    15% {
        transform: scale(1.05) scaleX(-1);
        filter: brightness(1.3) drop-shadow(0 0 15px rgba(255, 0, 85, 0.8));
    }
    40% {
        transform: scale(1.1) scaleX(-1);
        filter: brightness(1.5) drop-shadow(0 0 25px rgba(255, 0, 85, 1));
    }
    60% {
        transform: scale(1.08) scaleX(-1);
        filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 0, 85, 0.7));
    }
    80% {
        transform: scale(1.03) scaleX(-1);
        filter: brightness(1.1) drop-shadow(0 0 10px rgba(255, 0, 85, 0.5));
    }
    100% {
        transform: scale(1) scaleX(-1);
        filter: brightness(1) drop-shadow(0 0 5px rgba(255, 0, 85, 0.3));
    }
}

.damage-animation .damage-effect {
    animation: damage 0.6s ease-out;
}

@keyframes damage {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    30% {
        opacity: 1;
        transform: scale(1.2);
    }
    70% {
        opacity: 0.8;
        transform: scale(1.5);
    }
    100% {
        opacity: 0;
        transform: scale(2);
    }
}

@keyframes damage-pulse {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.3);
    }
    100% {
        opacity: 0;
        transform: scale(1.8);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.start-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #2fa3d7;
    margin-top: 20px;
    outline: none;
}
.start-btn:hover, .start-btn:focus {
    background: linear-gradient(145deg, #3fb3e7, #4fc3f7);
    box-shadow: 0 6px 0 #2fa3d7;
    transform: translateY(-2px);
}

.laser-beam-svg {
    pointer-events: none;
    z-index: 9999;
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
}

.laser-hit-effect {
    animation: laser-hit-burst 0.35s ease-out forwards;
}
@keyframes laser-hit-burst {
    0% {
        opacity: 0.85;
        transform: scale(0.7);
        filter: blur(0px);
    }
    60% {
        opacity: 1;
        transform: scale(1.2);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: scale(1.7);
        filter: blur(3px);
    }
}

/* Enhanced Laser Attack Animations */
@keyframes charging-pulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 20px currentColor, 0 0 40px currentColor;
    }
    50% { 
        transform: scale(1.3);
        box-shadow: 0 0 30px currentColor, 0 0 60px currentColor;
    }
}

@keyframes particle-charge {
    0% {
        transform: scale(0) translateY(0);
        opacity: 1;
    }
    50% {
        transform: scale(1) translateY(-20px);
        opacity: 0.8;
    }
    100% {
        transform: scale(0) translateY(-40px);
        opacity: 0;
    }
}

@keyframes trail-fade {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.5);
        opacity: 0;
    }
}

@keyframes shockwave {
    0% {
        transform: scale(0.5);
        opacity: 0.8;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

@keyframes impact-particle {
    0% {
        transform: scale(1) translateX(0) translateY(0);
        opacity: 1;
    }
    100% {
        transform: scale(0) translateX(var(--dx, 0)) translateY(var(--dy, 0));
        opacity: 0;
    }
}

/* Facial Expression Animations */
@keyframes victory-blink {
    0%, 90%, 100% { opacity: 1; }
    95% { opacity: 0.3; }
}

@keyframes defeat-blink {
    0%, 70%, 100% { opacity: 0.3; }
    85% { opacity: 0.1; }
}

@keyframes worried-blink {
    0%, 80%, 100% { opacity: 0.7; }
    90% { opacity: 0.4; }
}

@keyframes critical-blink {
    0%, 60%, 100% { opacity: 0.4; }
    80% { opacity: 0.1; }
}

/* Victory and defeat states - no bounce */
.robot.victory .robot-body {
    animation: victory-dance 2s ease-in-out infinite;
}

.robot.defeat .robot-body {
    animation: defeat-sag 1s ease-out forwards;
}

/* Enhanced damage effects with more dramatic sad appearance */
/* Light damage effects */
.player-robot.lightly-damaged .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M40,50 L50,40 M80,50 L70,40 M45,80 L55,70 M75,80 L65,70" stroke="rgba(255,193,7,0.6)" stroke-width="2" stroke-linecap="round"/><circle cx="60" cy="120" r="3" fill="rgba(255,193,7,0.4)"/></svg>');
    opacity: 0.6;
}

.enemy-robot.lightly-damaged .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M40,50 L50,40 M80,50 L70,40 M45,80 L55,70 M75,80 L65,70" stroke="rgba(255,193,7,0.6)" stroke-width="2" stroke-linecap="round"/><circle cx="60" cy="120" r="3" fill="rgba(255,193,7,0.4)"/></svg>');
    opacity: 0.6;
}

/* Medium damage effects */
.player-robot.damaged .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M30,40 L40,30 M90,40 L80,30 M35,60 L45,50 M85,60 L75,50 M25,80 L35,70 M95,80 L85,70 M20,100 L30,90 M100,100 L90,90" stroke="rgba(255,152,0,0.8)" stroke-width="2.5" stroke-linecap="round"/><circle cx="60" cy="110" r="4" fill="rgba(255,152,0,0.6)"/><circle cx="40" cy="70" r="2" fill="rgba(255,152,0,0.4)"/><circle cx="80" cy="70" r="2" fill="rgba(255,152,0,0.4)"/></svg>');
    opacity: 0.8;
}

.player-robot.critical .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M25,35 L35,25 M95,35 L85,25 M30,70 L45,55 M90,70 L75,55 M35,90 L50,75 M85,90 L70,75 M20,110 L30,100 M100,110 L90,100 M15,130 L25,120 M105,130 L95,120" stroke="rgba(244,67,54,1)" stroke-width="3.5" stroke-linecap="round"/><circle cx="60" cy="140" r="5" fill="rgba(244,67,54,0.7)"/><circle cx="40" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="80" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="30" cy="60" r="2" fill="rgba(244,67,54,0.3)"/><circle cx="90" cy="60" r="2" fill="rgba(244,67,54,0.3)"/></svg>');
    opacity: 1;
}

.enemy-robot.damaged .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M30,40 L40,30 M90,40 L80,30 M35,60 L45,50 M85,60 L75,50 M25,80 L35,70 M95,80 L85,70 M20,100 L30,90 M100,100 L90,90" stroke="rgba(255,152,0,0.8)" stroke-width="2.5" stroke-linecap="round"/><circle cx="60" cy="110" r="4" fill="rgba(255,152,0,0.6)"/><circle cx="40" cy="70" r="2" fill="rgba(255,152,0,0.4)"/><circle cx="80" cy="70" r="2" fill="rgba(255,152,0,0.4)"/></svg>');
    opacity: 0.8;
}

.enemy-robot.critical .damage-effect {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="160" viewBox="0 0 120 160"><path d="M25,35 L35,25 M95,35 L85,25 M30,70 L45,55 M90,70 L75,55 M35,90 L50,75 M85,90 L70,75 M20,110 L30,100 M100,110 L90,100 M15,130 L25,120 M105,130 L95,120" stroke="rgba(244,67,54,1)" stroke-width="3.5" stroke-linecap="round"/><circle cx="60" cy="140" r="5" fill="rgba(244,67,54,0.7)"/><circle cx="40" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="80" cy="90" r="3" fill="rgba(244,67,54,0.5)"/><circle cx="30" cy="60" r="2" fill="rgba(244,67,54,0.3)"/><circle cx="90" cy="60" r="2" fill="rgba(244,67,54,0.3)"/></svg>');
    opacity: 1;
}

/* Enhanced protection effect */
.protection-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    border: 3px solid #4CAF50;
    border-radius: 50%;
    animation: protection-shield 1s ease-out forwards;
    pointer-events: none;
}

@keyframes protection-shield {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Sad and tear animations */
@keyframes sad-tear {
    0%, 80% { opacity: 0.6; transform: translate(-50%, -50%) scaleY(1); }
    90% { opacity: 1; transform: translate(-50%, -50%) scaleY(1.2); }
    100% { opacity: 0.6; transform: translate(-50%, -50%) scaleY(1); }
}

@keyframes eye-tear {
    0%, 70% { opacity: 0.5; transform: translateX(-50%) scaleY(1); }
    85% { opacity: 1; transform: translateX(-50%) scaleY(1.3); }
    100% { opacity: 0.5; transform: translateX(-50%) scaleY(1); }
}

@keyframes crack-pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes critical-crack-pulse {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.15); }
}

@keyframes sad-blink {
    0%, 80%, 100% { opacity: 0.7; transform: scaleY(1); }
    90% { opacity: 0.4; transform: scaleY(0.8); }
}

@keyframes critical-sad-blink {
    0%, 60%, 100% { opacity: 0.4; transform: scaleY(1); }
    80% { opacity: 0.1; transform: scaleY(0.7); }
}

/* Additional sad effects for damaged robots */
/* Light damage antenna effects */
.robot.lightly-damaged .robot-antenna {
    transform: rotate(-4deg) scaleY(0.9);
    opacity: 0.85;
    animation: light-droop-antenna 3.5s ease-in-out infinite;
}

/* Medium damage antenna effects */
.robot.damaged .robot-antenna {
    transform: rotate(-8deg) scaleY(0.8);
    opacity: 0.7;
    animation: droop-antenna 2.5s ease-in-out infinite;
}

.robot.critical .robot-antenna {
    transform: rotate(-15deg) scaleY(0.6);
    opacity: 0.5;
    animation: critical-droop-antenna 1.8s ease-in-out infinite;
}

/* Light damage panel effects */
.robot.lightly-damaged .robot-panel {
    opacity: 0.8;
    animation: light-dim-panel 4s ease-in-out infinite;
}

/* Medium damage panel effects */
.robot.damaged .robot-panel {
    opacity: 0.6;
    animation: dim-panel 3s ease-in-out infinite;
}

.robot.critical .robot-panel {
    opacity: 0.3;
    animation: critical-dim-panel 1.5s ease-in-out infinite;
}

/* Light damage cheek effects */
.robot.lightly-damaged .robot-cheek {
    opacity: 0.7;
    transform: scale(0.9);
}

/* Medium damage cheek effects */
.robot.damaged .robot-cheek {
    opacity: 0.5;
    transform: scale(0.8);
}

.robot.critical .robot-cheek {
    opacity: 0.2;
    transform: scale(0.6);
}

/* Sad body animations */
/* Light damage body animation */
.robot.lightly-damaged .robot-body {
    animation: light-sad-sway 4s ease-in-out infinite;
}

/* Medium damage body animation */
.robot.damaged .robot-body {
    animation: sad-sway 3s ease-in-out infinite;
}

.robot.critical .robot-body {
    animation: critical-sad-sway 2s ease-in-out infinite;
}

/* Additional animations for sad effects */
/* Light damage animations */
@keyframes light-sad-blink {
    0%, 85%, 100% { opacity: 0.8; transform: scaleY(0.95); }
    92% { opacity: 0.6; transform: scaleY(0.9); }
}

@keyframes light-crack-pulse {
    0%, 100% { opacity: 0.2; transform: scale(1); }
    50% { opacity: 0.4; transform: scale(1.05); }
}

@keyframes light-droop-antenna {
    0%, 100% { transform: rotate(-4deg) scaleY(0.9); }
    50% { transform: rotate(-6deg) scaleY(0.85); }
}

@keyframes light-dim-panel {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.7; }
}

@keyframes light-sad-sway {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(1px) rotate(-0.5deg); }
    75% { transform: translateY(1px) rotate(0.5deg); }
}

/* Medium damage animations */
@keyframes droop-antenna {
    0%, 100% { transform: rotate(-8deg) scaleY(0.8); }
    50% { transform: rotate(-10deg) scaleY(0.75); }
}

.robot.critical .robot-antenna {
    animation: critical-droop-antenna 1.8s ease-in-out infinite;
}

@keyframes critical-droop-antenna {
    0%, 100% { transform: rotate(-15deg) scaleY(0.6); }
    50% { transform: rotate(-20deg) scaleY(0.5); }
}

@keyframes dim-panel {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.4; }
}

.robot.critical .robot-panel {
    animation: critical-dim-panel 1.5s ease-in-out infinite;
}

@keyframes critical-dim-panel {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.1; }
}

@keyframes sad-sway {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(2px) rotate(-1deg); }
    75% { transform: translateY(2px) rotate(1deg); }
}

@keyframes critical-sad-sway {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(3px) rotate(-2deg); }
    75% { transform: translateY(3px) rotate(2deg); }
}

@keyframes victory-dance {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-5px) rotate(-2deg); }
    75% { transform: translateY(-5px) rotate(2deg); }
}

@keyframes defeat-sag {
    0% { transform: translateY(0); }
    100% { transform: translateY(10px); }
} 