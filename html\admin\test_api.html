<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>API Test Page</h1>
    <p>Use this page to test the admin APIs and set up the database.</p>

    <!-- Database Setup Section -->
    <div class="test-section">
        <h2>Database Setup</h2>
        <p>First, set up the database with required tables:</p>
        <button onclick="setupDatabase()">Setup Database</button>
        <div id="setupResult"></div>
    </div>

    <!-- Users API Tests -->
    <div class="test-section">
        <h2>Users API Tests</h2>
        <button onclick="testGetAllUsers()">Get All Users</button>
        <button onclick="testSearchUsers()">Search Users (test: '1')</button>
        <button onclick="testGetUserDetails()">Get User Details (ID: 571483)</button>
        <div id="usersResult"></div>
    </div>

    <!-- Game Content API Tests -->
    <div class="test-section">
        <h2>Game Content API Tests</h2>
        <button onclick="testGetAllContent()">Get All Content</button>
        <button onclick="testGetContentByLevel()">Get Content by Level (Level 1)</button>
        <button onclick="testGetLevels()">Get Levels</button>
        <button onclick="testAddContent()" class="success">Add Test Question</button>
        <div id="gameResult"></div>
    </div>

    <!-- Manual Testing Section -->
    <div class="test-section">
        <h2>Manual Testing Links</h2>
        <p>After APIs are working, test the actual admin interfaces:</p>
        <a href="user-management.html" target="_blank">
            <button>Open User Management</button>
        </a>
        <a href="game-management.html" target="_blank">
            <button>Open Game Management</button>
        </a>
    </div>

    <script>
        const API_BASE_URL = '../../php/admin/';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            const statusClass = isError ? 'error' : 'success';
            const statusText = isError ? 'ERROR' : 'SUCCESS';
            
            element.innerHTML = `
                <div class="status ${statusClass}">
                    ${statusText}: ${isError ? data.message || 'Unknown error' : 'Request completed'}
                </div>
                <div class="result">${JSON.stringify(data, null, 2)}</div>
            `;
        }

        async function setupDatabase() {
            try {
                const response = await fetch('../../setup_database.php');
                const result = await response.json();
                showResult('setupResult', result, !result.success);
            } catch (error) {
                showResult('setupResult', { error: error.message }, true);
            }
        }

        async function testGetAllUsers() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
                const result = await response.json();
                showResult('usersResult', result, !result.success);
            } catch (error) {
                showResult('usersResult', { error: error.message }, true);
            }
        }

        async function testSearchUsers() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=search_users&search=1`);
                const result = await response.json();
                showResult('usersResult', result, !result.success);
            } catch (error) {
                showResult('usersResult', { error: error.message }, true);
            }
        }

        async function testGetUserDetails() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=get_user_details&user_id=571483`);
                const result = await response.json();
                showResult('usersResult', result, !result.success);
            } catch (error) {
                showResult('usersResult', { error: error.message }, true);
            }
        }

        async function testGetAllContent() {
            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
                const result = await response.json();
                showResult('gameResult', result, !result.success);
            } catch (error) {
                showResult('gameResult', { error: error.message }, true);
            }
        }

        async function testGetContentByLevel() {
            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_content_by_level&level=1`);
                const result = await response.json();
                showResult('gameResult', result, !result.success);
            } catch (error) {
                showResult('gameResult', { error: error.message }, true);
            }
        }

        async function testGetLevels() {
            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_levels`);
                const result = await response.json();
                showResult('gameResult', result, !result.success);
            } catch (error) {
                showResult('gameResult', { error: error.message }, true);
            }
        }

        async function testAddContent() {
            const testQuestion = {
                level_number: 1,
                question_text: "Test question: What is 2+2?",
                option1: "3",
                option2: "4",
                option3: "5",
                option4: "6",
                correct_answer: 2
            };

            try {
                const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testQuestion)
                });
                const result = await response.json();
                showResult('gameResult', result, !result.success);
            } catch (error) {
                showResult('gameResult', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
