<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Robot Battle Quiz - Answer questions correctly to defeat the enemy robot!">
    <title>Robot Battle</title>
    <link rel="stylesheet" href="../css/robot-battle.css">
</head>
<body data-level="2">
    <div class="game-container">
        <!-- Battle Arena -->
        <div class="battle-arena">
            <div class="player-side">
                <div class="robot player-robot">
                    <div class="robot-sprite">
                        <div class="robot-head">
                            <div class="robot-antenna"></div>
                            <div class="robot-eyes">
                                <div class="robot-eye"></div>
                                <div class="robot-eye"></div>
                            </div>
                            <div class="robot-mouth"></div>
                            <div class="robot-cheeks">
                                <div class="robot-cheek"></div>
                                <div class="robot-cheek"></div>
                            </div>
                        </div> 
                        <div class="robot-body">
                            <div class="robot-panel"></div>
                        </div>
                    </div>
                    <div class="damage-effect"></div>
                </div>
                <div class="health-container">
                    <div class="health-label">PLAYER</div>
                    <div class="health-bar-container">
                        <div class="health-bar player-health"></div>
                    </div>
                    <div class="health-value">100/100</div>
                </div>
            </div>

            <div class="vs-indicator">VS</div>

            <div class="enemy-side">
                <div class="robot enemy-robot">
                    <div class="robot-sprite">
                        <div class="robot-head">
                            <div class="robot-antenna"></div>
                            <div class="robot-eyes">
                                <div class="robot-eye"></div>
                                <div class="robot-eye"></div>
                            </div>
                            <div class="robot-mouth"></div>
                            <div class="robot-cheeks">
                                <div class="robot-cheek"></div>
                                <div class="robot-cheek"></div>
                            </div>
                        </div>
                        <div class="robot-body">
                            <div class="robot-panel"></div>
                        </div>
                    </div>
                    <div class="damage-effect"></div>
                </div>
                <div class="health-container">
                    <div class="health-label">ENEMY</div>
                    <div class="health-bar-container">
                        <div class="health-bar enemy-health"></div>
                    </div>
                    <div class="health-value">150/150</div>
                </div>
            </div>
        </div>

        <!-- Power Ups -->
        <div class="power-ups">
            <button class="power-up-btn" id="add-life" title="Add Life">
                <span class="power-up-icon">❤️</span>
                <span class="power-up-count">1</span>
            </button>
            <button class="power-up-btn" id="protect" title="Protect (Immune to next attack)">
                <span class="power-up-icon">🛡️</span>
                <span class="power-up-count">1</span>
            </button>
            <button class="power-up-btn" id="add-time" title="Add 10 Seconds">
                <span class="power-up-icon">⏱️</span>
                <span class="power-up-count">1</span>
            </button>
        </div>

        <!-- Question Area -->
        <div class="question-area">
            <div class="timer-container">
                <div class="timer">
                    <div class="timer-bar"></div>
                    <div class="timer-text">10</div>
                </div>
            </div>

            <div class="question-container">
                <h2 class="question-text">Question will appear here</h2>
                <div class="options-container">
                    <button class="option-btn" data-index="0">Option 1</button>
                    <button class="option-btn" data-index="1">Option 2</button>
                    <button class="option-btn" data-index="2">Option 3</button>
                    <button class="option-btn" data-index="3">Option 4</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Instruction Modal -->
    <div class="modal instructions-modal" id="instruction-modal" role="dialog" aria-labelledby="instruction-title">
        <div class="modal-content">
            <h2 id="instruction-title">Robot Battle Mission</h2>
            <div class="instructions">
                <p>Instruction</p>
                <ul>
                    <li>Answer each question correctly to attack the enemy robot, get it right, and you deal damage!</li> 
                    <li>If you answer wrong or run out of time, the enemy will attack you instead.</li>
                    <li>Win the battle by defeating the enemy robot before it defeats you!</li>
                </ul>
            </div>
            <button class="start-btn" id="start-btn" aria-label="Start Battle Mission">FIGHT!</button>
        </div>
    </div>

    <!-- Results Modal -->
    <div class="modal results-modal" id="results-modal" role="dialog" aria-labelledby="result-title">
        <div class="modal-content">
            <h2 id="result-title" class="result-title">VICTORY!</h2>
            <div id="result-stars" class="stars" aria-label="Star rating"></div>
            <p id="result-message" class="result-message">You defeated the enemy robot!</p>
            <div class="exp-container">
                <span class="exp-icon">✨</span>
                <span class="exp-text">+0 EXP</span>
            </div>
            <div class="modal-buttons">
                <button class="replay-btn" id="restart-btn">PLAY AGAIN</button>
                <button class="main-menu-btn" id="main-menu-btn">MAIN MENU</button>
            </div>
        </div>
    </div>

    <script src="../js/game2.js"></script>
</body>
</html>