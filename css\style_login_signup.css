@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&family=Uncial+Antique&family=MedievalSharp&display=swap');

/* Adventure Theme Variables */
:root {
    --parchment-light: #f4e4bc;
    --parchment-dark: #d4c4a0;
    --leather-brown: #8b4513;
    --gold-accent: #ffd700;
    --bronze-accent: #cd7f32;
    --mystical-blue: #1e3a8a;
    --deep-forest: #0f2027;
    --treasure-glow: #ffeb3b;
    --shadow-dark: rgba(0, 0, 0, 0.8);
    --border-ornate: #8b4513;
}

body{
    background: linear-gradient(135deg,
        var(--deep-forest) 0%,
        #203a43 25%,
        #2c5364 50%,
        #0f2027 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Magical floating particles background effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, var(--treasure-glow), transparent),
        radial-gradient(2px 2px at 40px 70px, var(--gold-accent), transparent),
        radial-gradient(1px 1px at 90px 40px, var(--treasure-glow), transparent),
        radial-gradient(1px 1px at 130px 80px, var(--gold-accent), transparent),
        radial-gradient(2px 2px at 160px 30px, var(--treasure-glow), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    opacity: 0.1;
    z-index: -1;
}

@keyframes sparkle {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

p{
    margin: 0px;
}

/* HEADER AND TITLE DESIGN */
.header{
    background: linear-gradient(45deg,
        rgba(139, 69, 19, 0.1) 0%,
        rgba(205, 127, 50, 0.1) 50%,
        rgba(139, 69, 19, 0.1) 100%);
    border-radius: 15px;
    display: flex;
    min-width: 100%;
    min-height: 20vh;
    margin-top: 2%;
    position: relative;
    backdrop-filter: blur(10px);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
    border-radius: 15px;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.header .title{
    padding-top: 10px;
    padding-left: 8%;
    z-index: 2;
    position: relative;
}

.body{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    min-height: 60vh;
    padding: 2rem 1rem;
}

.title{
    font-size: clamp(2.5rem, 8vw, 4rem);
    font-family: "Cinzel", "serif";
    color: var(--gold-accent);
    margin-bottom: 40px;
    text-shadow:
        0 0 10px var(--treasure-glow),
        0 0 20px var(--gold-accent),
        0 0 30px var(--gold-accent),
        2px 2px 4px var(--shadow-dark);
    position: relative;
    letter-spacing: 2px;
}

.title::before {
    content: '⚔️';
    position: absolute;
    left: -60px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.6em;
    animation: float 3s ease-in-out infinite;
}

.title::after {
    content: '🗡️';
    position: absolute;
    right: -60px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.6em;
    animation: float 3s ease-in-out infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translateY(-50%) rotate(-5deg); }
    50% { transform: translateY(-60%) rotate(5deg); }
}

.title a{
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.title a:hover {
    text-shadow:
        0 0 15px var(--treasure-glow),
        0 0 25px var(--gold-accent),
        0 0 35px var(--gold-accent),
        2px 2px 6px var(--shadow-dark);
}

/* ADVENTURE SCROLL DESIGN FOR LOGIN AND SIGNUP */
.login-cont, .signup-cont{
    background: linear-gradient(145deg,
        var(--parchment-light) 0%,
        var(--parchment-dark) 50%,
        var(--parchment-light) 100%);
    border: 3px solid var(--border-ornate);
    border-radius: 25px;
    padding: 2.5rem 2rem;
    color: var(--leather-brown);
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: scrollUnfurl 1.2s ease-out;
    position: relative;
    box-shadow:
        0 10px 30px var(--shadow-dark),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    backdrop-filter: blur(5px);
}

/* Ornate corner decorations */
.login-cont::before, .signup-cont::before,
.login-cont::after, .signup-cont::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid var(--bronze-accent);
    border-radius: 50%;
    background: radial-gradient(circle, var(--gold-accent) 30%, var(--bronze-accent) 70%);
}

.login-cont::before, .signup-cont::before {
    top: -15px;
    left: -15px;
    border-bottom: none;
    border-right: none;
}

.login-cont::after, .signup-cont::after {
    bottom: -15px;
    right: -15px;
    border-top: none;
    border-left: none;
}

@keyframes scrollUnfurl {
    0% {
        opacity: 0;
        transform: translateY(50px) rotateX(-15deg) scale(0.9);
        box-shadow: 0 5px 15px var(--shadow-dark);
    }
    50% {
        opacity: 0.7;
        transform: translateY(10px) rotateX(-5deg) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) rotateX(0deg) scale(1);
        box-shadow:
            0 10px 30px var(--shadow-dark),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    }
}

.login-cont .login-title, .signup-cont .signup-title{
    font-size: clamp(1.8rem, 5vw, 2.5rem);
    font-family: "Cinzel", "serif";
    margin: 0 0 2rem 0;
    color: var(--leather-brown);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    text-align: center;
    letter-spacing: 1px;
}

.login-cont .login-title::after, .signup-cont .signup-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--bronze-accent), transparent);
}

.login-cont form, .signup-cont form{
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 400px;
}

.login-cont form label, .signup-cont form label{
    font-size: 1.1rem;
    font-family: "Cinzel", "serif";
    margin: 0 0 0.5rem 0.5rem;
    color: var(--leather-brown);
    font-weight: 600;
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5);
}

.login-cont form input, .signup-cont form input{
    margin: 0 0 1.5rem 0;
    padding: 1rem 1.2rem;
    font-size: 1.1rem;
    font-family: "Cinzel", "serif";
    border-radius: 12px;
    border: 2px solid var(--bronze-accent);
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(244, 228, 188, 0.9) 100%);
    width: 100%;
    color: var(--leather-brown);
    box-shadow:
        inset 2px 2px 5px rgba(0, 0, 0, 0.1),
        0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.login-cont form input:focus, .signup-cont form input:focus {
    outline: none;
    border-color: var(--gold-accent);
    box-shadow:
        inset 2px 2px 5px rgba(0, 0, 0, 0.1),
        0 0 15px rgba(255, 215, 0, 0.3),
        0 2px 10px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.login-cont form input::placeholder, .signup-cont form input::placeholder {
    color: rgba(139, 69, 19, 0.6);
    font-style: italic;
}

.btn-cont{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    margin-bottom: 0;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-cont button{
    background: linear-gradient(145deg,
        var(--bronze-accent) 0%,
        var(--leather-brown) 50%,
        var(--bronze-accent) 100%);
    color: var(--parchment-light);
    font-size: 1rem;
    font-family: "Cinzel", "serif";
    font-weight: 600;
    border-radius: 25px;
    padding: 0.8rem 2rem;
    border: 2px solid var(--gold-accent);
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-cont button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-cont button:hover::before {
    left: 100%;
}

.btn-cont button:hover {
    transform: translateY(-3px);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: var(--treasure-glow);
}

.btn-cont button:active {
    transform: translateY(-1px);
    box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-cont a{
    font-size: 0.9rem;
    font-family: "Cinzel", "serif";
    color: var(--mystical-blue);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.btn-cont a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--leather-brown);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.btn-cont a:hover {
    color: var(--leather-brown);
    text-shadow: var(--shadow-dark);
}

.btn-cont a:hover::after {
    width: 100%;
}