# Testing Guide

## Prerequisites

1. **XAMPP Setup**:
   - Ensure XAMPP is installed and running
   - Apache and MySQL services should be active
   - Database `dbfunconnect` should exist

2. **File Structure**:
   - All files should be in the correct locations as implemented
   - Ensure proper file permissions

## Step-by-Step Testing

### Step 1: Database Setup

1. **Open the API Test Page**:
   ```
   http://localhost/dashboard/CapstoneProject2/html/admin/test_api.html
   ```

2. **Run Database Setup**:
   - Click "Setup Database" button
   - Should see success message indicating table creation
   - If successful, sample questions will be inserted

3. **Verify Database**:
   - Open phpMyAdmin: `http://localhost/phpmyadmin`
   - Navigate to `dbfunconnect` database
   - Confirm `game_content` table exists with sample data

### Step 2: Test APIs

Using the test page, verify each API endpoint:

#### Users API:
1. **Get All Users**: Should return list of existing users
2. **Search Users**: Should filter users based on search term
3. **Get User Details**: Should return detailed user information

#### Game Content API:
1. **Get All Content**: Should return all questions from game_content table
2. **Get Content by Level**: Should return questions for specific level
3. **Get Levels**: Should return summary of available levels
4. **Add Test Question**: Should successfully add a new question

### Step 3: Test User Management Interface

1. **Open User Management**:
   ```
   http://localhost/dashboard/CapstoneProject2/html/admin/user-management.html
   ```

2. **Verify Functionality**:
   - Users should display in grid format
   - Search functionality should work
   - Pagination should work if many users
   - Click on user cards to view details modal
   - User details should show complete information

3. **Expected Behavior**:
   - No JavaScript errors in browser console
   - Users load automatically on page load
   - All user data displays correctly

### Step 4: Test Game Management Interface

1. **Open Game Management**:
   ```
   http://localhost/dashboard/CapstoneProject2/html/admin/game-management.html
   ```

2. **Test New Editing Workflow**:

   **a. View Questions**:
   - Levels should display with questions
   - Click level headers to expand/collapse
   - Questions should show with options and correct answer marked

   **b. Edit Questions (New Workflow)**:
   - Click "Edit" button on any question
   - Modal should open with question data
   - Make changes to question text or options
   - Click "OK" button (not "Save")
   - Modal should close
   - Question should show "Pending Changes" indicator
   - Level should show "Unsaved Changes" indicator
   - "Save Changes" and "Discard" buttons should appear

   **c. Batch Save**:
   - Edit multiple questions in the same level
   - Each should show pending changes
   - Click "Save Changes" at level level
   - All changes should be saved to database
   - Indicators should disappear
   - Refresh page to verify changes persisted

   **d. Discard Changes**:
   - Edit some questions
   - Click "Discard" button
   - Confirm discard action
   - All pending changes should be lost
   - Questions should revert to original state

   **e. Add New Questions**:
   - Click "Add Question" button
   - Fill out form completely
   - Click "Add Question" button
   - Question should be saved immediately (no pending state)
   - New question should appear in the level

### Step 5: Visual Verification

1. **Pending Changes Styling**:
   - Questions with pending changes should have yellow left border
   - "Pending Changes" text should be visible and yellow
   - Level should show pulsing "Unsaved Changes" indicator

2. **Button States**:
   - "Save Changes" button should be green
   - "Discard" button should be gray
   - Buttons should only appear when there are pending changes

3. **Responsive Design**:
   - Test on different screen sizes
   - Mobile layout should work properly

## Troubleshooting

### Common Issues:

1. **Database Connection Errors**:
   - Verify XAMPP MySQL is running
   - Check database name in `php/dbconnection.php`
   - Ensure database `dbfunconnect` exists

2. **No Users Displaying**:
   - Check if `user_account` table has data
   - Verify API responses in browser network tab
   - Check browser console for JavaScript errors

3. **No Game Content**:
   - Run database setup script
   - Verify `game_content` table exists and has data
   - Check API responses

4. **JavaScript Errors**:
   - Open browser developer tools (F12)
   - Check Console tab for errors
   - Verify all JavaScript files are loading correctly

5. **Styling Issues**:
   - Verify CSS file is loading
   - Check for CSS syntax errors
   - Clear browser cache

### Debug Steps:

1. **Check API Responses**:
   - Use browser Network tab to see API calls
   - Verify responses are valid JSON
   - Check for HTTP error codes

2. **Check Database**:
   - Use phpMyAdmin to verify table structure
   - Check if data exists in tables
   - Verify column names match code expectations

3. **Check File Paths**:
   - Verify all file references are correct
   - Check case sensitivity in file names
   - Ensure proper directory structure

## Expected Results

After successful testing:

1. **User Management**: 
   - All existing users display correctly
   - Search and pagination work
   - User details modal shows complete information

2. **Game Management**:
   - Questions display organized by levels
   - New editing workflow works as specified
   - Pending changes are tracked and can be batch saved
   - Add question functionality works immediately
   - Visual indicators show pending states clearly

3. **No Errors**:
   - No JavaScript console errors
   - No PHP errors or warnings
   - All API endpoints respond correctly
