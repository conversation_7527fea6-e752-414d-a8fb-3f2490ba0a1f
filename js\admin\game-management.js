// Global variables
let gameLevels = [];
let filteredGameLevels = [];
let gameContent = [];
let pendingChanges = {}; // Track pending changes by level
let editingQuestions = {}; // Track questions being edited

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Initialize game content data
async function initializeGameData() {
    try {
        await loadGameContent();
        displayGameLevels();
    } catch (error) {
        console.error('Error initializing game data:', error);
        showNotification('Error loading game content from database', 'error');
    }
}

// Load game content from API
async function loadGameContent() {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
        const result = await response.json();

        if (result.success) {
            gameContent = result.data;
            processGameLevels();
        } else {
            throw new Error(result.error || 'Failed to load game content');
        }
    } catch (error) {
        console.error('Error loading game content:', error);
        showNotification(error.message || 'Error loading game content', 'error');
        gameContent = [];
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Process game content into levels
function processGameLevels() {
    const levelGroups = {};

    gameContent.forEach(content => {
        const levelNum = content.level_number;
        if (!levelGroups[levelNum]) {
            levelGroups[levelNum] = {
                levelID: levelNum,
                levelName: `Level ${levelNum}`,
                isActive: true,
                questions: []
            };
        }

        levelGroups[levelNum].questions.push({
            id: content.content_id,
            question: content.question_text,
            options: [content.option1, content.option2, content.option3, content.option4],
            correctAnswer: content.correct_answer
        });
    });

    gameLevels = Object.values(levelGroups).sort((a, b) => a.levelID - b.levelID);
    filteredGameLevels = [...gameLevels];

    if (gameLevels.length === 0) {
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Display game levels
function displayGameLevels() {
    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    if (filteredGameLevels.length === 0) {
        const isSearching = document.getElementById('levelSearch').value.trim() !== '';
        levelsContainer.innerHTML = `
            <div class="empty-levels-state">
                <div class="empty-icon">
                    <i class="fas fa-${isSearching ? 'search' : 'gamepad'}"></i>
                </div>
                <h3>${isSearching ? 'No Levels Found' : 'No Game Levels Found'}</h3>
                <p>${isSearching ? 'No levels match your search criteria. Try different keywords.' : 'No levels with questions have been created yet. Add some questions to create levels.'}</p>
                ${!isSearching ? '<button class="btn-primary" onclick="showAddQuestionModal(1)"><i class="fas fa-plus"></i> Add First Question</button>' : ''}
            </div>
        `;
        return;
    }

    filteredGameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });
}

// Create level card HTML
function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        const optionLabels = ['A', 'B', 'C', 'D'];
        const optionsHtml = question.options.map((option, index) => {
            const correctIndex = typeof question.correctAnswer === 'string'
                ? optionLabels.indexOf(question.correctAnswer.toUpperCase())
                : question.correctAnswer - 1;
            const isCorrect = index === correctIndex;
            return `<li>
                <span class="option-label">${optionLabels[index]}:</span>
                <span class="option-text">${option}</span>
                ${isCorrect ? '<span class="correct-answer">Correct</span>' : ''}
            </li>`;
        }).join('');

        const isPending = pendingChanges[level.levelID] && pendingChanges[level.levelID][question.id];
        const isEditing = editingQuestions[question.id];

        return `<div class="question-item ${isPending ? 'pending-changes' : ''} ${isEditing ? 'editing' : ''}" id="question-${question.id}">
            <div class="question-header">
                <div class="question-text">${question.question}</div>
                <div class="question-actions">
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="question-meta">
                <span>Level ${level.levelID}</span>
                ${isPending ? '<span class="pending-indicator">• Pending Changes</span>' : ''}
            </div>
            <ul class="options-list">
                ${optionsHtml}
            </ul>
        </div>`;
    }).join('');

    const hasPendingChanges = pendingChanges[level.levelID] && Object.keys(pendingChanges[level.levelID]).length > 0;

    card.innerHTML = `
        <div class="level-header" onclick="toggleLevelDropdown(${level.levelID})">
            <div class="level-info">
                <div class="level-title-row">
                    <h3>${level.levelName}</h3>
                    <div class="level-meta">
                        <span class="questions-count">${level.questions.length} questions</span>
                        <span class="level-status ${level.isActive ? 'active' : 'inactive'}">
                            ${level.isActive ? 'Active' : 'Inactive'}
                        </span>
                        ${hasPendingChanges ? '<span class="pending-changes-indicator">• Unsaved Changes</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="level-actions" onclick="event.stopPropagation()">
                <button class="btn-primary btn-small" onclick="showAddQuestionModal(${level.levelID})">
                    <i class="fas fa-plus"></i> Add Question
                </button>
                ${hasPendingChanges ? `
                <button class="btn-success btn-small" onclick="saveLevelChanges(${level.levelID})">
                    <i class="fas fa-save"></i> Save Changes
                </button>
                <button class="btn-secondary btn-small" onclick="discardLevelChanges(${level.levelID})">
                    <i class="fas fa-times"></i> Discard
                </button>
                ` : ''}
            </div>
            <button class="dropdown-toggle" id="dropdown-toggle-${level.levelID}">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="level-questions collapsed ${level.questions.length === 0 ? 'empty' : ''}" id="level-questions-${level.levelID}">
            ${level.questions.length === 0 ?
                '<div class="empty-questions">No questions added yet. Click "Add Question" to get started.</div>' :
                questionsHtml
            }
        </div>
    `;

    return card;
}

// Search levels
function searchLevels() {
    const searchTerm = document.getElementById('levelSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredGameLevels = [...gameLevels];
    } else {
        filteredGameLevels = gameLevels.filter(level => {
            return level.levelID.toString().includes(searchTerm);
        });
    }

    displayGameLevels();
}

// Toggle level dropdown
function toggleLevelDropdown(levelID) {
    const questionsContainer = document.getElementById(`level-questions-${levelID}`);
    const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

    if (questionsContainer && dropdownToggle) {
        if (questionsContainer.classList.contains('collapsed')) {
            questionsContainer.classList.remove('collapsed');
            questionsContainer.classList.add('expanded');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        } else {
            questionsContainer.classList.remove('expanded');
            questionsContainer.classList.add('collapsed');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
        }
    }
}

// Toggle all levels
function toggleAllLevels() {
    const toggleButton = document.getElementById('toggleAllLevels');

    if (filteredGameLevels.length === 0) {
        return;
    }

    const isExpanding = toggleButton.textContent.includes('Expand');

    filteredGameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${level.levelID}`);

        if (questionsContainer && dropdownToggle) {
            if (isExpanding) {
                questionsContainer.classList.remove('collapsed');
                questionsContainer.classList.add('expanded');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
            } else {
                questionsContainer.classList.remove('expanded');
                questionsContainer.classList.add('collapsed');
                dropdownToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
            }
        }
    });

    if (isExpanding) {
        toggleButton.innerHTML = '<i class="fas fa-compress-alt"></i> Collapse All';
    } else {
        toggleButton.innerHTML = '<i class="fas fa-expand-alt"></i> Expand All';
    }
}

// Show add question modal
function showAddQuestionModal(levelID) {
    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('levelNumber').value = levelID;
    document.getElementById('contentId').value = '';
    document.getElementById('questionForm').reset();

    // Update modal buttons for adding mode
    updateModalButtons(false, null, levelID);

    modal.classList.add('show');
}

// Update modal buttons based on mode (add vs edit)
function updateModalButtons(isEditMode, questionId, levelID) {
    const formActions = document.querySelector('.form-actions');

    if (isEditMode) {
        formActions.innerHTML = `
            <button type="button" class="btn-secondary" onclick="cancelEdit(${questionId})">Cancel</button>
            <button type="button" class="btn-primary" onclick="confirmEdit(${questionId}, ${levelID})">OK</button>
        `;
    } else {
        formActions.innerHTML = `
            <button type="button" class="btn-secondary" onclick="closeModal('questionModal')">Cancel</button>
            <button type="submit" class="btn-primary">Add Question</button>
        `;
    }
}

// Edit question
function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    const question = level.questions.find(q => q.id === questionId);
    if (!question) return;

    // Mark question as being edited
    editingQuestions[questionId] = true;

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('contentId').value = questionId;
    document.getElementById('questionText').value = question.question;
    document.getElementById('option1').value = question.options[0];
    document.getElementById('option2').value = question.options[1];
    document.getElementById('option3').value = question.options[2];
    document.getElementById('option4').value = question.options[3];

    // Handle both letter (A,B,C,D) and number (1,2,3,4) correct answers
    let correctAnswer = question.correctAnswer;
    if (typeof correctAnswer === 'string') {
        correctAnswer = ['A', 'B', 'C', 'D'].indexOf(correctAnswer.toUpperCase()) + 1;
    }
    document.getElementById('correctAnswer').value = correctAnswer;

    document.getElementById('levelNumber').value = levelID;

    // Update modal buttons for editing mode
    updateModalButtons(true, questionId, levelID);

    modal.classList.add('show');
}

// Handle add question form submission (immediate save)
async function handleAddQuestion(event) {
    event.preventDefault();

    const questionText = document.getElementById('questionText').value;
    const option1 = document.getElementById('option1').value;
    const option2 = document.getElementById('option2').value;
    const option3 = document.getElementById('option3').value;
    const option4 = document.getElementById('option4').value;
    const correctAnswer = document.getElementById('correctAnswer').value;
    const levelNumber = document.getElementById('levelNumber').value;

    const questionData = {
        level_number: parseInt(levelNumber),
        question_text: questionText,
        option1: option1,
        option2: option2,
        option3: option3,
        option4: option4,
        correct_answer: parseInt(correctAnswer)
    };

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(questionData)
        });
        const result = await response.json();

        if (result.success) {
            showNotification('Question added successfully', 'success');
            closeModal('questionModal');
            await loadGameContent(); // Refresh data
            displayGameLevels();
        } else {
            throw new Error(result.error || 'Failed to add question');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification(error.message || 'An error occurred', 'error');
    }
}

// Confirm edit (store changes locally, don't save to database yet)
function confirmEdit(questionId, levelID) {
    const questionText = document.getElementById('questionText').value;
    const option1 = document.getElementById('option1').value;
    const option2 = document.getElementById('option2').value;
    const option3 = document.getElementById('option3').value;
    const option4 = document.getElementById('option4').value;
    const correctAnswer = document.getElementById('correctAnswer').value;

    // Store pending changes
    if (!pendingChanges[levelID]) {
        pendingChanges[levelID] = {};
    }

    pendingChanges[levelID][questionId] = {
        question_text: questionText,
        option1: option1,
        option2: option2,
        option3: option3,
        option4: option4,
        correct_answer: parseInt(correctAnswer),
        level_number: levelID
    };

    // Remove from editing state
    delete editingQuestions[questionId];

    closeModal('questionModal');
    displayGameLevels(); // Refresh display to show pending changes
    showNotification('Changes saved locally. Click "Save Changes" to apply to database.', 'info');
}

// Cancel edit
function cancelEdit(questionId) {
    delete editingQuestions[questionId];
    closeModal('questionModal');
    displayGameLevels(); // Refresh display
}

// Save all pending changes for a level
async function saveLevelChanges(levelID) {
    const changes = pendingChanges[levelID];
    if (!changes || Object.keys(changes).length === 0) {
        showNotification('No changes to save', 'info');
        return;
    }

    try {
        const promises = [];

        for (const [questionId, questionData] of Object.entries(changes)) {
            const promise = fetch(`${API_BASE_URL}game_content_api.php?action=update_content`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content_id: parseInt(questionId),
                    ...questionData
                })
            });
            promises.push(promise);
        }

        const responses = await Promise.all(promises);
        const results = await Promise.all(responses.map(r => r.json()));

        const failedUpdates = results.filter(r => !r.success);

        if (failedUpdates.length === 0) {
            // Clear pending changes for this level
            delete pendingChanges[levelID];

            showNotification(`Successfully saved ${Object.keys(changes).length} question(s)`, 'success');
            await loadGameContent(); // Refresh data
            displayGameLevels();
        } else {
            throw new Error(`Failed to save ${failedUpdates.length} question(s)`);
        }
    } catch (error) {
        console.error('Error saving changes:', error);
        showNotification(error.message || 'Error saving changes', 'error');
    }
}

// Discard pending changes for a level
function discardLevelChanges(levelID) {
    if (confirm('Are you sure you want to discard all unsaved changes for this level?')) {
        delete pendingChanges[levelID];
        displayGameLevels(); // Refresh display
        showNotification('Changes discarded', 'info');
    }
}

// Delete question
async function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question?')) {
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=delete_content&content_id=${questionId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                showNotification('Question deleted successfully', 'success');
                await loadGameContent(); // Refresh data
                displayGameLevels();
            } else {
                throw new Error(result.error || 'Failed to delete question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
        }
    }
}

// Close modal
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeGameData);

// Add form submit event listener
document.addEventListener('DOMContentLoaded', function() {
    const questionForm = document.getElementById('questionForm');
    if (questionForm) {
        questionForm.addEventListener('submit', handleAddQuestion);
    }
});
