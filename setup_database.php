<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Database connection
    $pdo = new PDO('mysql:host=localhost;dbname=dbfunconnect', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create game_content table
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS `game_content` (
      `content_id` int(11) NOT NULL AUTO_INCREMENT,
      `level_number` int(11) NOT NULL,
      `question_text` text NOT NULL,
      `option1` varchar(255) NOT NULL,
      `option2` varchar(255) NOT NULL,
      `option3` varchar(255) NOT NULL,
      `option4` varchar(255) NOT NULL,
      `correct_answer` int(1) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`content_id`),
      KEY `level_number` (`level_number`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ";
    
    $pdo->exec($createTableSQL);
    
    // Check if table has data
    $stmt = $pdo->query("SELECT COUNT(*) FROM game_content");
    $count = $stmt->fetchColumn();
    
    // Insert sample data if table is empty
    if ($count == 0) {
        $insertSQL = "
        INSERT INTO `game_content` (`level_number`, `question_text`, `option1`, `option2`, `option3`, `option4`, `correct_answer`) VALUES
        (1, 'What is the primary function of a network cable?', 'To provide power to devices', 'To transmit data between devices', 'To protect devices from viruses', 'To store data permanently', 2),
        (1, 'Which type of cable is commonly used for Ethernet connections?', 'Coaxial cable', 'Fiber optic cable', 'Twisted pair cable', 'USB cable', 3),
        (1, 'What does RJ45 refer to in networking?', 'A type of router', 'A network protocol', 'A connector type', 'A cable length standard', 3),
        (2, 'What is the maximum length for a Cat5e cable run?', '50 meters', '100 meters', '200 meters', '500 meters', 2),
        (2, 'Which cable category supports Gigabit Ethernet?', 'Cat3', 'Cat5', 'Cat5e', 'All of the above', 3),
        (2, 'What is crosstalk in network cables?', 'Signal interference between wires', 'Cable bending', 'Connector corrosion', 'Power fluctuation', 1);
        ";
        
        $pdo->exec($insertSQL);
        echo json_encode([
            'success' => true, 
            'message' => 'Database setup completed successfully. Created game_content table and inserted sample data.',
            'records_inserted' => 6
        ]);
    } else {
        echo json_encode([
            'success' => true, 
            'message' => 'Database setup completed successfully. game_content table already exists.',
            'existing_records' => $count
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'error' => 'Error: ' . $e->getMessage()
    ]);
}
?>
