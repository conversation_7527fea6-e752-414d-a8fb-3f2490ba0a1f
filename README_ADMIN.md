# Admin System - User & Game Management

## Overview

This admin system provides comprehensive management capabilities for users and game content with the following key features:

### User Management
- View all registered users with detailed information
- Search and filter users
- View user progress, achievements, and statistics
- Delete users with proper cascade handling

### Game Management
- Manage game questions organized by levels
- **New Enhanced Editing Workflow**:
  - Edit questions without immediate auto-save
  - Batch save multiple changes at once
  - Visual indicators for pending changes
  - Discard unwanted changes

## Quick Start

### 1. Database Setup
Visit: `http://localhost/dashboard/CapstoneProject2/html/admin/test_api.html`
- Click "Setup Database" to create required tables
- Verify all API tests pass

### 2. Access Admin Interfaces

**User Management:**
```
http://localhost/dashboard/CapstoneProject2/html/admin/user-management.html
```

**Game Management:**
```
http://localhost/dashboard/CapstoneProject2/html/admin/game-management.html
```

## New Game Management Workflow

### Traditional Workflow (Add Questions):
1. Click "Add Question" → Opens form
2. Fill details → Click "Add Question"
3. ✅ **Immediately saved to database**

### New Enhanced Workflow (Edit Questions):
1. Click "Edit" on existing question → Opens form with current data
2. Make changes → Click "OK" 
3. 🟡 **Changes stored locally** (not saved to database yet)
4. Question shows "Pending Changes" indicator
5. Level shows "Unsaved Changes" with "Save Changes" button
6. Repeat steps 1-4 for multiple questions in same level
7. Click "Save Changes" → ✅ **All pending changes saved to database**

### Benefits:
- **Batch Operations**: Edit multiple questions before saving
- **Change Review**: See all pending changes before committing
- **Error Prevention**: Discard unwanted changes easily
- **Better UX**: Clear visual feedback on what's changed

## File Structure

```
CapstoneProject2/
├── html/admin/
│   ├── user-management.html      # User management interface
│   ├── game-management.html      # Game content management
│   └── test_api.html            # API testing & database setup
├── js/admin/
│   ├── admin-dashboard.js       # User management logic
│   └── game-management.js       # Game management logic (enhanced)
├── css/admin/
│   └── admin-dashboard.css      # Styling (includes pending changes)
├── php/admin/
│   ├── users_api.php           # User management API (fixed)
│   └── game_content_api.php    # Game content API
├── php/
│   └── dbconnection.php        # Database connection
├── setup_database.php          # Database setup script
└── Documentation/
    ├── IMPLEMENTATION_SUMMARY.md
    ├── TESTING_GUIDE.md
    └── README_ADMIN.md (this file)
```

## Key Features

### User Management:
- ✅ Fixed database schema mismatches
- ✅ Proper error handling
- ✅ Search and pagination
- ✅ Detailed user information display

### Game Management:
- ✅ Enhanced editing workflow with batch save
- ✅ Visual indicators for pending changes
- ✅ Discard changes functionality
- ✅ Level-based organization
- ✅ Add new questions (immediate save)

## Technical Details

### Database Requirements:
- Database: `dbfunconnect`
- Required tables: `user_account`, `user_tier`, `user_levels`, `game_content`
- Auto-created: `game_content` table via setup script

### API Endpoints:

**Users API** (`php/admin/users_api.php`):
- `GET ?action=get_all_users` - List all users
- `GET ?action=search_users&search=term` - Search users
- `GET ?action=get_user_details&user_id=123` - User details
- `DELETE ?action=delete_user&user_id=123` - Delete user

**Game Content API** (`php/admin/game_content_api.php`):
- `GET ?action=get_all_content` - All questions
- `GET ?action=get_content_by_level&level=1` - Level questions
- `POST ?action=add_content` - Add new question
- `PUT ?action=update_content` - Update question
- `DELETE ?action=delete_content&content_id=123` - Delete question

## Troubleshooting

### Common Issues:
1. **No users showing**: Check database connection and user_account table
2. **No game content**: Run database setup script
3. **JavaScript errors**: Check browser console, verify file paths
4. **API errors**: Use test_api.html to debug endpoints

### Support:
- Check `TESTING_GUIDE.md` for detailed testing instructions
- Use `test_api.html` for API debugging
- Review `IMPLEMENTATION_SUMMARY.md` for technical details

## Browser Compatibility
- Chrome/Edge: ✅ Fully supported
- Firefox: ✅ Fully supported  
- Safari: ✅ Should work (not extensively tested)
- IE: ❌ Not supported (uses modern JavaScript)
